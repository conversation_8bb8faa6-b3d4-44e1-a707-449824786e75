<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Monaco + Emmet</title>
  <style>
    body, html { margin: 0; height: 100%; }
    #editor { width: 100%; height: 100%; }
  </style>
</head>
<body>
  <div id="editor"></div>

  <!-- Monaco Editor CDN -->
  <script src="https://cdn.jsdelivr.net/npm/monaco-editor@0.44.0/min/vs/loader.js"></script>

  <!-- Emmet for Monaco CDN -->
  <script src="https://cdn.jsdelivr.net/npm/@emmetio/monaco-plugin@1.2.1/dist/emmet-monaco-plugin.min.js"></script>

  <script>
    require.config({ paths: { 'vs': 'https://cdn.jsdelivr.net/npm/monaco-editor@0.44.0/min/vs' }});
    require(['vs/editor/editor.main'], function() {
      // Initialize Monaco editor
      const editor = monaco.editor.create(document.getElementById('editor'), {
        value: '<!DOCTYPE html>\n<html>\n  <head>\n  </head>\n  <body>\n  </body>\n</html>',
        language: 'html',
        theme: 'vs-dark',
      });

      // Load Emmet
      emmetMonaco.emmetHTML(monaco);
    });
  </script>
</body>
</html>